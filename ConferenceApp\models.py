from django.db import models
from django.utils import timezone

class Category(models.Model):
    title = models.CharField(max_length=200)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
class Conference(models.Model):
    title = models.CharField(max_length=250)
    description = models.TextField(blank=True)
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(default=timezone.now)
    location = models.CharField(max_length=250)
    price = models.FloatField()
    capacity = models.IntegerField()
    program = models.FileField(upload_to='files/')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='conferences')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
