from django.db import models
from django.contrib.auth.models import AbstractUser

class Participant(AbstractUser):
    cin = models.CharField(max_length=8, primary_key=True)
    email = models.EmailField(max_length=150, unique=True)
    first_name = models.Char<PERSON><PERSON>(max_length=200)
    last_name = models.Char<PERSON><PERSON>(max_length=200)
    username = models.Char<PERSON><PERSON>(unique=True,max_length=150)

    CHOICE = (
        ('ETUDIANT', 'Etudiant'),
        ('CHERCHEUR', 'Chercheur'),
        ('ENSEIGNANT', 'Enseignant'),
        ('DOCTEUR', 'Docteur'),
    )
    participant_category = models.CharField('category', max_length=100, choices=CHOICE)
    USERNAME_FIELD = 'username'



